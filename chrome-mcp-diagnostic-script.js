/**
 * Chrome MCP诊断脚本
 * 用于检查服务配置面板显示问题
 * 在浏览器控制台中运行此脚本
 */

console.log('🔍 开始Chrome MCP诊断 - 服务配置面板显示问题');
console.log('=====================================');

// 检查主页面的服务配置面板
function checkServicePanel() {
    console.log('\n1️⃣ === 服务配置面板基础检查 ===');
    
    // 查找服务配置面板
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    console.log('服务配置面板元素:', servicePanel);
    
    if (!servicePanel) {
        console.error('❌ 服务配置面板不存在！');
        return null;
    }
    
    console.log('✅ 服务配置面板存在');
    console.log('面板ID:', servicePanel.id);
    console.log('面板类名:', servicePanel.className);
    
    // 检查计算样式
    const styles = getComputedStyle(servicePanel);
    console.log('\n📏 计算样式:');
    console.log('display:', styles.display);
    console.log('visibility:', styles.visibility);
    console.log('opacity:', styles.opacity);
    console.log('position:', styles.position);
    console.log('z-index:', styles.zIndex);
    console.log('background:', styles.background);
    console.log('transform:', styles.transform);
    
    // 检查位置和尺寸
    const rect = servicePanel.getBoundingClientRect();
    console.log('\n📐 位置尺寸:');
    console.log('位置: (', rect.left.toFixed(1), ',', rect.top.toFixed(1), ')');
    console.log('尺寸:', rect.width.toFixed(1), '×', rect.height.toFixed(1));
    
    // 检查是否在视口内
    const inViewport = rect.top >= 0 && rect.left >= 0 && 
                      rect.bottom <= window.innerHeight && rect.right <= window.innerWidth;
    console.log('在视口内:', inViewport ? '✅ 是' : '❌ 否');
    
    return servicePanel;
}

// 检查语言下拉菜单
function checkLanguageDropdown() {
    console.log('\n2️⃣ === 语言下拉菜单检查 ===');
    
    const dropdown = document.querySelector('#languagesDropdown');
    const trigger = document.querySelector('#languagesTrigger');
    const options = document.querySelector('#languagesOptions');
    
    console.log('下拉菜单容器:', dropdown);
    console.log('触发器:', trigger);
    console.log('选项容器:', options);
    
    if (!dropdown || !trigger || !options) {
        console.error('❌ 下拉菜单元素不完整！');
        return null;
    }
    
    console.log('✅ 下拉菜单元素完整');
    
    // 检查选项容器的样式
    const optionsStyles = getComputedStyle(options);
    console.log('\n📏 下拉选项样式:');
    console.log('display:', optionsStyles.display);
    console.log('opacity:', optionsStyles.opacity);
    console.log('visibility:', optionsStyles.visibility);
    console.log('position:', optionsStyles.position);
    console.log('z-index:', optionsStyles.zIndex);
    console.log('top:', optionsStyles.top);
    console.log('left:', optionsStyles.left);
    
    return { dropdown, trigger, options };
}

// 测试下拉菜单交互
function testDropdownInteraction() {
    console.log('\n3️⃣ === 下拉菜单交互测试 ===');
    
    const trigger = document.querySelector('#languagesTrigger');
    const options = document.querySelector('#languagesOptions');
    
    if (!trigger || !options) {
        console.error('❌ 无法进行交互测试');
        return;
    }
    
    console.log('点击触发器...');
    trigger.click();
    
    setTimeout(() => {
        const optionsRect = options.getBoundingClientRect();
        const optionsStyles = getComputedStyle(options);
        
        console.log('下拉菜单状态:');
        console.log('- 是否显示:', optionsStyles.display !== 'none');
        console.log('- 透明度:', optionsStyles.opacity);
        console.log('- 可见性:', optionsStyles.visibility);
        console.log('- 位置:', optionsRect.left.toFixed(1), ',', optionsRect.top.toFixed(1));
        console.log('- 尺寸:', optionsRect.width.toFixed(1), '×', optionsRect.height.toFixed(1));
    }, 100);
}

// 检查遮挡情况
function checkOverlap() {
    console.log('\n4️⃣ === 遮挡情况检查 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    const options = document.querySelector('#languagesOptions');
    
    if (!servicePanel || !options) {
        console.error('❌ 无法进行遮挡检查');
        return;
    }
    
    const servicePanelRect = servicePanel.getBoundingClientRect();
    const optionsRect = options.getBoundingClientRect();
    
    console.log('服务面板位置:', servicePanelRect.left.toFixed(1), ',', servicePanelRect.top.toFixed(1));
    console.log('服务面板尺寸:', servicePanelRect.width.toFixed(1), '×', servicePanelRect.height.toFixed(1));
    console.log('下拉菜单位置:', optionsRect.left.toFixed(1), ',', optionsRect.top.toFixed(1));
    console.log('下拉菜单尺寸:', optionsRect.width.toFixed(1), '×', optionsRect.height.toFixed(1));
    
    // 检查重叠
    const isOverlapping = !(servicePanelRect.right < optionsRect.left || 
                          servicePanelRect.left > optionsRect.right || 
                          servicePanelRect.bottom < optionsRect.top || 
                          servicePanelRect.top > optionsRect.bottom);
    
    console.log('\n🎯 重叠检测:', isOverlapping ? '❌ 存在重叠' : '✅ 无重叠');
    
    if (isOverlapping) {
        // 计算重叠区域
        const overlapLeft = Math.max(servicePanelRect.left, optionsRect.left);
        const overlapRight = Math.min(servicePanelRect.right, optionsRect.right);
        const overlapTop = Math.max(servicePanelRect.top, optionsRect.top);
        const overlapBottom = Math.min(servicePanelRect.bottom, optionsRect.bottom);
        
        const overlapWidth = overlapRight - overlapLeft;
        const overlapHeight = overlapBottom - overlapTop;
        
        console.log('重叠区域尺寸:', overlapWidth.toFixed(1), '×', overlapHeight.toFixed(1));
        console.log('重叠百分比:', ((overlapWidth * overlapHeight) / (servicePanelRect.width * servicePanelRect.height) * 100).toFixed(1) + '%');
    }
}

// 应用临时修复
function applyTemporaryFixes() {
    console.log('\n5️⃣ === 应用临时修复 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    if (!servicePanel) {
        console.error('❌ 无法应用修复');
        return;
    }
    
    console.log('应用修复前的样式:');
    const beforeStyles = getComputedStyle(servicePanel);
    console.log('- background:', beforeStyles.background);
    console.log('- z-index:', beforeStyles.zIndex);
    console.log('- opacity:', beforeStyles.opacity);
    
    // 应用修复
    servicePanel.style.background = 'rgba(255, 255, 255, 0.95)';
    servicePanel.style.border = '2px solid #9F299F';
    servicePanel.style.position = 'relative';
    servicePanel.style.zIndex = '1001';
    servicePanel.style.boxShadow = '0 4px 20px rgba(159, 41, 159, 0.3)';
    
    console.log('✅ 临时修复已应用:');
    console.log('- 背景不透明度提升至95%');
    console.log('- 添加紫色边框');
    console.log('- 设置相对定位');
    console.log('- Z-index提升至1001');
    console.log('- 添加彩色阴影');
    
    setTimeout(() => {
        const afterStyles = getComputedStyle(servicePanel);
        console.log('\n修复后的样式:');
        console.log('- background:', afterStyles.background);
        console.log('- z-index:', afterStyles.zIndex);
        console.log('- opacity:', afterStyles.opacity);
        console.log('- border:', afterStyles.border);
    }, 100);
}

// 生成问题报告
function generateProblemReport() {
    console.log('\n6️⃣ === 问题诊断报告 ===');
    
    const servicePanel = document.querySelector('[data-panel="service-config"]');
    if (!servicePanel) {
        console.log('❌ 主要问题: 服务配置面板元素不存在');
        return;
    }
    
    const styles = getComputedStyle(servicePanel);
    const rect = servicePanel.getBoundingClientRect();
    
    let problems = [];
    let solutions = [];
    
    // 透明度检查
    if (parseFloat(styles.opacity) < 0.9) {
        problems.push('背景透明度过低 (' + styles.opacity + ')');
        solutions.push('增加背景不透明度至95%以上');
    }
    
    // Z-index检查
    if (styles.zIndex === 'auto' || parseInt(styles.zIndex) < 1000) {
        problems.push('Z-index层级不足 (' + styles.zIndex + ')');
        solutions.push('设置z-index为1001或更高');
    }
    
    // 可见性检查
    if (styles.display === 'none') {
        problems.push('元素被设置为display: none');
        solutions.push('移除或修复display: none规则');
    }
    
    if (styles.visibility === 'hidden') {
        problems.push('元素被设置为visibility: hidden');
        solutions.push('移除或修复visibility: hidden规则');
    }
    
    // 位置检查
    const inViewport = rect.top >= 0 && rect.left >= 0 && 
                      rect.bottom <= window.innerHeight && rect.right <= window.innerWidth;
    if (!inViewport) {
        problems.push('元素位置超出视口范围');
        solutions.push('调整元素位置或检查定位CSS');
    }
    
    console.log('🚨 发现的问题:');
    if (problems.length === 0) {
        console.log('✅ 未发现明显问题');
    } else {
        problems.forEach((problem, index) => {
            console.log(`${index + 1}. ${problem}`);
        });
    }
    
    console.log('\n💡 建议解决方案:');
    solutions.forEach((solution, index) => {
        console.log(`${index + 1}. ${solution}`);
    });
    
    // 边框修复建议
    console.log('3. 添加明显边框增强可见性');
    console.log('4. 确保下拉菜单使用智能定位避免遮挡');
}

// 主诊断函数
function runFullDiagnostic() {
    console.clear();
    console.log('🔍 === Chrome MCP 完整诊断开始 ===');
    console.log('时间:', new Date().toLocaleString());
    console.log('页面URL:', window.location.href);
    console.log('视口尺寸:', window.innerWidth, '×', window.innerHeight);
    
    // 1. 基础检查
    const servicePanel = checkServicePanel();
    
    // 2. 下拉菜单检查
    checkLanguageDropdown();
    
    // 3. 交互测试
    testDropdownInteraction();
    
    // 4. 遮挡检查
    setTimeout(() => {
        checkOverlap();
        
        // 5. 问题报告
        generateProblemReport();
        
        console.log('\n🎯 === 诊断完成 ===');
        console.log('如需应用临时修复，请运行: applyTemporaryFixes()');
        console.log('如需重新诊断，请运行: runFullDiagnostic()');
        
    }, 500);
}

// 暴露全局函数
window.chromeMCPDiagnostic = {
    runFullDiagnostic,
    checkServicePanel,
    checkLanguageDropdown,
    testDropdownInteraction,
    checkOverlap,
    applyTemporaryFixes,
    generateProblemReport
};

// 自动运行诊断
console.log('\n🚀 自动运行完整诊断...');
console.log('如需手动控制，使用: window.chromeMCPDiagnostic.functionName()');
runFullDiagnostic();