<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <!-- 加载CSS -->
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="container">
        <h1>OTA系统修复验证</h1>
        
        <!-- 价格信息面板测试 -->
        <section class="panel compact-card" data-panel="price-info" role="region" aria-label="价格信息区域" tabindex="0">
            <div class="section-header">
                <h3 data-i18n="form.priceInfo">💰 价格信息</h3>
            </div>
            <div class="panel-content compact-inline-layout">
                <div class="form-group icon-inline" id="otaPriceGroup">
                    <span class="field-icon" data-i18n-aria="form.otaPrice" aria-label="OTA价格">💵</span>
                    <div class="compact-price-input">
                        <input type="number" id="otaPrice" data-i18n-aria="form.otaPrice" aria-label="OTA价格" data-i18n="form.otaPrice" placeholder="OTA价格" data-i18n-title="form.otaPriceTooltip" title="OTA订单价格" step="0.01" min="0">
                        <select id="currency" data-i18n-aria="form.currency" aria-label="货币" data-i18n-title="form.currencyTooltip" title="选择货币">
                            <option value="MYR">MYR</option>
                            <option value="USD">USD</option>
                            <option value="SGD">SGD</option>
                            <option value="CNY">CNY</option>
                        </select>
                    </div>
                </div>

                <div class="form-group icon-inline" id="driverFeeGroup">
                    <span class="field-icon" data-i18n-aria="form.driverFee" aria-label="司机费用">🚗</span>
                    <input type="number" id="driverFee" data-i18n-aria="form.driverFee" aria-label="司机费用" data-i18n="form.driverFee" placeholder="司机费用" data-i18n-title="form.driverFeeTooltip" title="司机服务费用" step="0.01" min="0">
                </div>
            </div>
        </section>
        
        <!-- 额外要求文本框测试 -->
        <section class="panel compact-card" data-panel="extra-requirements" role="region" aria-label="额外要求区域" tabindex="0">
            <div class="section-header">
                <h3 data-i18n="form.extraRequirement">📝 额外要求</h3>
            </div>
            <div class="panel-content compact-inline-layout">
                <div class="form-group">
                    <textarea id="extraRequirement" rows="2" data-i18n="form.extraRequirementPlaceholder" placeholder="其他特殊要求或备注" data-i18n-title="form.extraRequirementTooltip" title="其他特殊要求或备注"></textarea>
                </div>
            </div>
        </section>
        
        <!-- 语言切换测试 -->
        <div style="margin: 20px 0;">
            <button onclick="switchLanguage('zh-CN')">中文</button>
            <button onclick="switchLanguage('en-US')">English</button>
        </div>
        
        <!-- 测试说明 -->
        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9;">
            <h3>测试项目：</h3>
            <ol>
                <li><strong>价格信息优化：</strong> 
                    - 检查价格字段只显示图标，无额外文字标签
                    - 点击语言切换按钮，检查placeholder是否正确切换中英文（"OTA价格"/"OTA Price", "司机费用"/"Driver Fee"）
                    - 确认布局更紧凑，没有多余的占位空间</li>
                <li><strong>自适应高度：</strong> 在额外要求文本框中输入多行文本，检查高度是否自动调整</li>
                <li><strong>响应式：</strong> 调整浏览器窗口大小，检查移动端视图下的表现</li>
            </ol>
        </div>
    </div>
    
    <!-- 加载JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/managers/form-manager.js"></script>
    
    <script>
        // 简化的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化国际化
            if (window.OTA && window.OTA.I18nManager) {
                const i18n = new window.OTA.I18nManager();
                i18n.init();
                window.i18n = i18n;
            }
            
            // 初始化表单管理器（主要是为了自适应高度功能）
            if (window.OTA && window.OTA.managers && window.OTA.managers.FormManager) {
                const formManager = new window.OTA.managers.FormManager();
                formManager.initAutoResizeTextarea();
                window.formManager = formManager;
            }
        });
        
        // 语言切换函数
        function switchLanguage(lang) {
            if (window.i18n) {
                window.i18n.setLanguage(lang);
            }
        }
    </script>
</body>
</html>