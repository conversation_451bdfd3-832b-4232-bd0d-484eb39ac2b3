<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试下拉菜单修复</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-title {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .fix-summary {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            color: #155724;
        }
        
        .service-config {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        
        .service-config h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .language-dropdown {
            margin: 20px 0;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-success {
            background: #4caf50;
            color: white;
        }
        
        .status-error {
            background: #f44336;
            color: white;
        }
        
        .test-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">🔧 下拉菜单修复验证测试</div>
        
        <div class="fix-summary">
            <h4>✅ 已修复的问题：</h4>
            <ul>
                <li><strong>语法错误</strong>：修复了第331-332行多余的关闭括号</li>
                <li><strong>缺失方法</strong>：添加了 populateOptions() 和 bindOptionEvents() 方法</li>
                <li><strong>定位遮挡</strong>：实现了智能空间检测避免内容遮挡</li>
                <li><strong>自动关闭</strong>：修复了事件协调器初始化并添加降级方案</li>
            </ul>
        </div>
        
        <div class="test-instructions">
            <h4>测试步骤：</h4>
            <ol>
                <li>点击"语言选择"下拉菜单，检查是否正常打开</li>
                <li>确认服务配置面板内容在下拉菜单打开时仍然可见</li>
                <li>点击页面其他区域，检查下拉菜单是否自动关闭</li>
                <li>观察控制台日志，确认事件协调器正常工作</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🎯 关键测试场景</h3>
            
            <!-- 服务配置面板 - 这个内容之前会被遮挡 -->
            <div class="service-config">
                <h3>🚀 服务配置面板</h3>
                <p><strong>这个内容应该始终可见</strong></p>
                <p>当语言下拉菜单打开时，这段文字应该不会被遮挡或消失。</p>
                <div>状态：<span id="visibility-status" class="status-indicator status-success">内容可见</span></div>
            </div>
            
            <!-- 语言选择下拉菜单 -->
            <div class="language-dropdown">
                <label for="languagesSelect"><strong>🌍 语言选择：</strong></label>
                <div id="languagesOptions" class="multi-select-dropdown">
                    <div class="multi-select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                        <span class="multi-select-text placeholder">请选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options" role="listbox">
                        <div class="multi-select-option" role="option" tabindex="0" data-value="1">
                            <input type="checkbox" class="multi-select-checkbox" id="languagesOptions_option_0" value="1">
                            <label class="multi-select-label" for="languagesOptions_option_0">中文</label>
                        </div>
                        <div class="multi-select-option" role="option" tabindex="0" data-value="2">
                            <input type="checkbox" class="multi-select-checkbox" id="languagesOptions_option_1" value="2">
                            <label class="multi-select-label" for="languagesOptions_option_1">English</label>
                        </div>
                        <div class="multi-select-option" role="option" tabindex="0" data-value="3">
                            <input type="checkbox" class="multi-select-checkbox" id="languagesOptions_option_2" value="3">
                            <label class="multi-select-label" for="languagesOptions_option_2">Bahasa Malaysia</label>
                        </div>
                        <div class="multi-select-option" role="option" tabindex="0" data-value="4">
                            <input type="checkbox" class="multi-select-checkbox" id="languagesOptions_option_3" value="4">
                            <label class="multi-select-label" for="languagesOptions_option_3">Tamil</label>
                        </div>
                    </div>
                    <select name="languages[]" multiple style="display: none;">
                        <option value="">请选择语言</option>
                        <option value="1">中文</option>
                        <option value="2">English</option>
                        <option value="3">Bahasa Malaysia</option>
                        <option value="4">Tamil</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 系统状态</h3>
            <div>
                <strong>事件协调器状态：</strong>
                <span id="coordinator-status" class="status-indicator status-error">检测中...</span>
            </div>
            <div style="margin-top: 10px;">
                <strong>下拉菜单状态：</strong>
                <span id="dropdown-status" class="status-indicator status-error">未初始化</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="test-log" class="test-log">等待测试开始...</div>
        </div>
    </div>

    <!-- 引入必要的CSS和JS文件 -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/components/forms.css">
    
    <script>
        // 模拟核心依赖
        window.OTA = {};
        
        // 简单的Logger实现
        class SimpleLogger {
            log(message, level = 'info', data = null) {
                const timestamp = new Date().toLocaleTimeString();
                const logMessage = `[${timestamp}] [${level}] ${message}`;
                console.log(logMessage, data || '');
                
                // 更新测试日志
                const logElement = document.getElementById('test-log');
                if (logElement) {
                    logElement.innerHTML += `<div>${logMessage}</div>`;
                    logElement.scrollTop = logElement.scrollHeight;
                }
            }
        }
        
        window.getLogger = () => new SimpleLogger();
        
        // 状态监控
        function updateStatus() {
            // 检查事件协调器状态
            const coordinatorExists = !!(window.OTA?.globalEventCoordinator || window.globalEventCoordinator);
            const coordinatorInitialized = coordinatorExists && 
                (window.OTA?.globalEventCoordinator?.initialized || window.globalEventCoordinator?.initialized);
            
            const coordinatorStatus = document.getElementById('coordinator-status');
            if (coordinatorInitialized) {
                coordinatorStatus.textContent = '✅ 已初始化';
                coordinatorStatus.className = 'status-indicator status-success';
            } else if (coordinatorExists) {
                coordinatorStatus.textContent = '⚠️ 已创建未初始化';
                coordinatorStatus.className = 'status-indicator status-error';
            } else {
                coordinatorStatus.textContent = '❌ 不存在';
                coordinatorStatus.className = 'status-indicator status-error';
            }
            
            // 检查下拉菜单状态
            const dropdown = window.languageDropdown;
            const dropdownStatus = document.getElementById('dropdown-status');
            if (dropdown) {
                dropdownStatus.textContent = dropdown.isOpen ? '🔓 已打开' : '🔒 已关闭';
                dropdownStatus.className = 'status-indicator status-success';
            }
        }
        
        // 监控服务配置面板可见性
        function monitorVisibility() {
            const serviceConfig = document.querySelector('.service-config');
            const visibilityStatus = document.getElementById('visibility-status');
            
            if (serviceConfig) {
                const computedStyle = getComputedStyle(serviceConfig);
                const isVisible = computedStyle.display !== 'none' && 
                                computedStyle.visibility !== 'hidden' && 
                                computedStyle.opacity !== '0';
                
                if (isVisible) {
                    visibilityStatus.textContent = '✅ 内容可见';
                    visibilityStatus.className = 'status-indicator status-success';
                } else {
                    visibilityStatus.textContent = '❌ 内容被遮挡';
                    visibilityStatus.className = 'status-indicator status-error';
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 开始测试页面初始化');
            
            // 定期更新状态
            setInterval(() => {
                updateStatus();
                monitorVisibility();
            }, 1000);
        });
    </script>
    
    <!-- 引入事件协调器 -->
    <script src="js/core/global-event-coordinator.js"></script>
    
    <!-- 引入下拉菜单组件 -->
    <script src="js/multi-select-dropdown.js"></script>
    
    <script>
        // 等待组件加载完成后初始化
        setTimeout(() => {
            try {
                // 初始化事件协调器
                if (window.OTA?.globalEventCoordinator) {
                    window.OTA.globalEventCoordinator.init();
                    console.log('✅ 事件协调器已初始化');
                }
                
                // 初始化下拉菜单
                window.languageDropdown = new MultiSelectDropdown('languagesOptions', {
                    placeholder: '请选择语言',
                    maxDisplayItems: 2
                });
                
                console.log('✅ 下拉菜单已初始化');
                
                // 监听下拉菜单状态变化
                const trigger = document.querySelector('#languagesOptions .multi-select-trigger');
                if (trigger) {
                    const observer = new MutationObserver(() => {
                        updateStatus();
                    });
                    
                    observer.observe(trigger, { 
                        attributes: true, 
                        attributeFilter: ['aria-expanded'] 
                    });
                }
                
            } catch (error) {
                console.error('❌ 初始化失败:', error);
            }
        }, 500);
    </script>
</body>
</html>